<?php
require_once '../../includes/init.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_POST['action'] ?? '';

// تشخيص البيانات المستلمة
error_log("POST data: " . print_r($_POST, true));

$db = new Database();

try {
    switch ($action) {
        case 'delete':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // التحقق من وجود مركبات مرتبطة بهذا السائق
            $db->query("SELECT COUNT(*) as count FROM transport_vehicles WHERE driver_id = :id");
            $db->bind(':id', $id);
            $result = $db->single();
            
            if ($result['count'] > 0) {
                // تحديث المركبات لإزالة ربطها بالسائق
                $db->query("UPDATE transport_vehicles SET driver_id = NULL WHERE driver_id = :id");
                $db->bind(':id', $id);
                $db->execute();
            }

            // حذف السائق
            $db->query("DELETE FROM transport_drivers WHERE id = :id");
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم حذف السائق بنجاح']);
            } else {
                throw new Exception('فشل في حذف السائق');
            }
            break;

        case 'update':
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $license_number = trim($_POST['license_number'] ?? '');
            $license_type = trim($_POST['license_type'] ?? 'private');
            $license_expiry = trim($_POST['license_expiry'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $governorate = trim($_POST['governorate'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $region = trim($_POST['region'] ?? 'center');
            $status = trim($_POST['status'] ?? 'available');
            $experience_years = (int)($_POST['experience_years'] ?? 0);
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }
            if (empty($name)) {
                throw new Exception('اسم السائق مطلوب');
            }
            if (empty($phone)) {
                throw new Exception('رقم الهاتف مطلوب');
            }
            if (empty($license_number)) {
                throw new Exception('رقم الرخصة مطلوب');
            }

            // التحقق من عدم تكرار رقم الرخصة
            $db->query("SELECT id FROM transport_drivers WHERE license_number = :license_number AND id != :id");
            $db->bind(':license_number', $license_number);
            $db->bind(':id', $id);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('رقم الرخصة موجود بالفعل');
            }

            // تحديث السائق
            $db->query("
                UPDATE transport_drivers
                SET name = :name, phone = :phone, license_number = :license_number,
                    license_type = :license_type, license_expiry = :license_expiry,
                    address = :address, governorate = :governorate, city = :city, region = :region,
                    status = :status, experience_years = :experience_years,
                    is_active = :is_active, updated_at = NOW()
                WHERE id = :id
            ");
            $db->bind(':name', $name);
            $db->bind(':phone', $phone);
            $db->bind(':license_number', $license_number);
            $db->bind(':license_type', $license_type);
            $db->bind(':license_expiry', $license_expiry ?: null);
            $db->bind(':address', $address);
            $db->bind(':governorate', $governorate);
            $db->bind(':city', $city);
            $db->bind(':region', $region);
            $db->bind(':status', $status);
            $db->bind(':experience_years', $experience_years);
            $db->bind(':is_active', $is_active);
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات السائق بنجاح']);
            } else {
                throw new Exception('فشل في تحديث بيانات السائق');
            }
            break;

        case 'add':
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $license_number = trim($_POST['license_number'] ?? '');
            $license_type = trim($_POST['license_type'] ?? 'private');
            $license_expiry = trim($_POST['license_expiry'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $governorate = trim($_POST['governorate'] ?? '');
            $city = trim($_POST['city'] ?? '');
            $region = trim($_POST['region'] ?? 'center');
            $status = trim($_POST['status'] ?? 'available');
            $experience_years = (int)($_POST['experience_years'] ?? 0);
            $rating = 5.00; // تقييم افتراضي للسائق الجديد
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // معلومات المركبة (اختيارية)
            $vehicle_plate = trim($_POST['vehicle_plate'] ?? '');
            $vehicle_model = trim($_POST['vehicle_model'] ?? '');
            $vehicle_year = (int)($_POST['vehicle_year'] ?? 0);
            $vehicle_color = trim($_POST['vehicle_color'] ?? '');
            $vehicle_capacity = (int)($_POST['vehicle_capacity'] ?? 0);
            $transport_type_id = (int)($_POST['transport_type_id'] ?? 0);

            if (empty($name)) {
                throw new Exception('اسم السائق مطلوب');
            }
            if (empty($phone)) {
                throw new Exception('رقم الهاتف مطلوب');
            }
            if (empty($license_number)) {
                throw new Exception('رقم الرخصة مطلوب');
            }

            // التحقق من عدم تكرار رقم الرخصة
            $db->query("SELECT id FROM transport_drivers WHERE license_number = :license_number");
            $db->bind(':license_number', $license_number);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('رقم الرخصة موجود بالفعل');
            }

            // بدء المعاملة
            $db->query("START TRANSACTION");
            $db->execute();

            // إضافة سائق جديد
            $db->query("
                INSERT INTO transport_drivers (name, phone, license_number, license_type, license_expiry,
                                             address, governorate, city, region, status,
                                             experience_years, rating, is_active, created_at, updated_at)
                VALUES (:name, :phone, :license_number, :license_type, :license_expiry,
                        :address, :governorate, :city, :region, :status,
                        :experience_years, :rating, :is_active, NOW(), NOW())
            ");
            $db->bind(':name', $name);
            $db->bind(':phone', $phone);
            $db->bind(':license_number', $license_number);
            $db->bind(':license_type', $license_type);
            $db->bind(':license_expiry', $license_expiry ?: null);
            $db->bind(':address', $address);
            $db->bind(':governorate', $governorate);
            $db->bind(':city', $city);
            $db->bind(':region', $region);
            $db->bind(':status', $status);
            $db->bind(':experience_years', $experience_years);
            $db->bind(':rating', $rating);
            $db->bind(':is_active', $is_active);

            if (!$db->execute()) {
                throw new Exception('فشل في إضافة السائق');
            }

            $driver_id = $db->lastInsertId();

            // إضافة المركبة إذا تم توفير بياناتها
            if (!empty($vehicle_plate) && !empty($vehicle_model) && $transport_type_id > 0) {
                // التحقق من عدم تكرار رقم اللوحة
                $db->query("SELECT id FROM transport_vehicles WHERE plate_number = :plate_number");
                $db->bind(':plate_number', $vehicle_plate);
                $existing_vehicle = $db->single();

                if ($existing_vehicle) {
                    throw new Exception('رقم لوحة المركبة موجود بالفعل');
                }

                $db->query("
                    INSERT INTO transport_vehicles (driver_id, transport_type_id, plate_number, model,
                                                   year, color, capacity, is_active, created_at, updated_at)
                    VALUES (:driver_id, :transport_type_id, :plate_number, :model,
                            :year, :color, :capacity, 1, NOW(), NOW())
                ");
                $db->bind(':driver_id', $driver_id);
                $db->bind(':transport_type_id', $transport_type_id);
                $db->bind(':plate_number', $vehicle_plate);
                $db->bind(':model', $vehicle_model);
                $db->bind(':year', $vehicle_year ?: null);
                $db->bind(':color', $vehicle_color);
                $db->bind(':capacity', $vehicle_capacity);

                if (!$db->execute()) {
                    throw new Exception('فشل في إضافة المركبة');
                }
            }

            // تأكيد المعاملة
            $db->query("COMMIT");
            $db->execute();

            $message = 'تم إضافة السائق بنجاح';
            if (!empty($vehicle_plate)) {
                $message .= ' مع مركبته';
            }

            echo json_encode(['success' => true, 'message' => $message]);
            break;

        case 'add_vehicle':
            $driver_id = (int)($_POST['driver_id'] ?? 0);
            $transport_type_id = (int)($_POST['transport_type_id'] ?? 0);
            $plate_number = trim($_POST['plate_number'] ?? '');
            $model = trim($_POST['model'] ?? '');
            $year = (int)($_POST['year'] ?? 0);
            $color = trim($_POST['color'] ?? '');
            $capacity = (int)($_POST['capacity'] ?? 0);

            if ($driver_id <= 0) {
                throw new Exception('معرف السائق غير صحيح');
            }
            if ($transport_type_id <= 0) {
                throw new Exception('نوع المركبة مطلوب');
            }
            if (empty($plate_number)) {
                throw new Exception('رقم اللوحة مطلوب');
            }
            if (empty($model)) {
                throw new Exception('موديل المركبة مطلوب');
            }
            if ($capacity <= 0) {
                throw new Exception('سعة المركبة مطلوبة');
            }

            // التحقق من عدم تكرار رقم اللوحة
            $db->query("SELECT id FROM transport_vehicles WHERE plate_number = :plate_number");
            $db->bind(':plate_number', $plate_number);
            $existing = $db->single();

            if ($existing) {
                throw new Exception('رقم اللوحة موجود بالفعل');
            }

            // إضافة المركبة
            $db->query("
                INSERT INTO transport_vehicles (driver_id, transport_type_id, plate_number, model,
                                               year, color, capacity, is_active, created_at, updated_at)
                VALUES (:driver_id, :transport_type_id, :plate_number, :model,
                        :year, :color, :capacity, 1, NOW(), NOW())
            ");
            $db->bind(':driver_id', $driver_id);
            $db->bind(':transport_type_id', $transport_type_id);
            $db->bind(':plate_number', $plate_number);
            $db->bind(':model', $model);
            $db->bind(':year', $year ?: null);
            $db->bind(':color', $color);
            $db->bind(':capacity', $capacity);

            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة المركبة بنجاح']);
            } else {
                throw new Exception('فشل في إضافة السائق');
            }
            break;

        case 'get':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            $db->query("
                SELECT 
                    td.*,
                    tv.plate_number,
                    tv.model,
                    tv.year,
                    tty.name as vehicle_type
                FROM transport_drivers td
                LEFT JOIN transport_vehicles tv ON td.id = tv.driver_id
                LEFT JOIN transport_types tty ON tv.transport_type_id = tty.id
                WHERE td.id = :id
            ");
            $db->bind(':id', $id);
            $driver = $db->single();
            
            if (!$driver) {
                throw new Exception('السائق غير موجود');
            }

            echo json_encode(['success' => true, 'data' => $driver]);
            break;

        case 'toggle_status':
            $id = (int)($_POST['id'] ?? 0);
            $new_status = trim($_POST['new_status'] ?? '');

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            $valid_statuses = ['available', 'busy', 'offline'];
            if (!in_array($new_status, $valid_statuses)) {
                throw new Exception('حالة غير صحيحة');
            }

            $db->query("UPDATE transport_drivers SET status = :status, updated_at = NOW() WHERE id = :id");
            $db->bind(':status', $new_status);
            $db->bind(':id', $id);

            if ($db->execute()) {
                $status_names = [
                    'available' => 'متاح',
                    'busy' => 'مشغول',
                    'offline' => 'غير متصل'
                ];
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة السائق إلى: ' . $status_names[$new_status]]);
            } else {
                throw new Exception('فشل في تغيير حالة السائق');
            }
            break;

        case 'toggle_active_status':
            $id = (int)($_POST['id'] ?? 0);
            $new_active_status = $_POST['new_active_status'] === 'true' ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // تحديث حالة تفعيل السائق
            $db->query("UPDATE transport_drivers SET is_active = :is_active, updated_at = NOW() WHERE id = :id");
            $db->bind(':is_active', $new_active_status);
            $db->bind(':id', $id);

            if ($db->execute()) {
                $status_text = $new_active_status ? 'مفعل' : 'معطل';
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة تفعيل السائق إلى: ' . $status_text]);
            } else {
                throw new Exception('فشل في تغيير حالة تفعيل السائق');
            }
            break;

        default:
            throw new Exception('عملية غير مدعومة');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
